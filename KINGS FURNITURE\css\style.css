/* Global Styles */


:root {
    --primary-color: #0e1a42;
    --secondary-color: #00a0e3;
    --accent-color: #ffd700;
    --text-color: #333;
    --light-text: #fff;
    --light-bg: #f8f9fa;
    --dark-bg: #1a1a1a;
    --border-color: #e1e1e1;
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'Playfair Display', serif;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 15px;
}

h1 {
    font-size: 3.5rem;
    font-family: var(--font-secondary);
}

h2 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    font-family: var(--font-secondary);
}

h2:after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background-color: var(--accent-color);
    margin: 15px auto 0;
}

p {
    margin-bottom: 20px;
}

.section-description {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 40px;
    color: #666;
}

/* Buttons */
.btn-primary {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 12px 30px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

.btn-secondary {
    display: inline-block;
    background-color: transparent;
    color: var(--primary-color);
    padding: 10px 25px;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
    transform: translateY(-3px);
}

.btn-tertiary {
    display: inline-block;
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 8px 20px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

.btn-tertiary:hover {
    background-color: var(--secondary-color);
    color: var(--light-text);
}

/* Header */
header {
    background-color: transparent;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

.logo-container {
    flex: 0 0 220px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo {
    max-height: 100px;
    object-fit: contain;
    border-radius: 4px;
    transition: var(--transition);
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    font-weight: 500;
    position: relative;
    color: var(--light-text);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--accent-color);
}

.nav-links a.active:after,
.nav-links a:hover:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--accent-color);
}









/* New Arrivals Section */
.new-arrivals {
    padding: 80px 0;
    background-color: var(--light-bg);
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 40px;
}

.product-card {
    background-color: var(--light-text);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--accent-color);
    color: var(--primary-color);
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.8rem;
}

.product-card h3 {
    padding: 15px 15px 5px;
    font-size: 1.2rem;
}

.product-description {
    padding: 0 15px;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.product-meta {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 15px 15px;
}

.product-meta span {
    color: #666;
    font-size: 0.9rem;
}

.product-meta .fa-star {
    color: var(--accent-color);
}

.add-to-cart {
    background-color: var(--primary-color);
    color: var(--light-text);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.add-to-cart:hover {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.view-more {
    text-align: center;
}

/* Catalogue Page Styles */
.catalogue-page header {
    position: relative;
    background-color: var(--primary-color);
}

/* Catalogue Layout */
.catalogue-layout {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    gap: 30px;
    padding: 0 20px;
}

/* Catalogue Sidebar */
.catalogue-sidebar {
    width: 280px;
    flex-shrink: 0;
    background: var(--light-bg);
    border-radius: 8px;
    padding: 25px;
    height: fit-content;
    position: sticky;
    top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.catalogue-sidebar h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

.category-list {
    list-style: none;
    margin-bottom: 30px;
}

.category-list li {
    margin-bottom: 8px;
}

.category-link {
    display: block;
    padding: 12px 15px;
    color: var(--text-color);
    text-decoration: none;
    border-radius: 6px;
    transition: var(--transition);
    font-weight: 500;
}

.category-link:hover,
.category-link.active {
    background-color: var(--primary-color);
    color: var(--light-text);
    transform: translateX(5px);
}

.sidebar-contact {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    color: var(--light-text);
}

.sidebar-contact h4 {
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.sidebar-contact p {
    margin-bottom: 15px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.whatsapp-sidebar-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #25D366;
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
}

.whatsapp-sidebar-btn:hover {
    background: #128C7E;
    transform: scale(1.05);
}

/* Main Content Area */
.catalogue-main {
    flex: 1;
    min-width: 0;
}

/* Mobile Sidebar Toggle */
.mobile-sidebar-toggle {
    display: none;
}

.mobile-sidebar-btn {
    background: var(--primary-color);
    color: var(--light-text);
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.mobile-sidebar-btn:hover {
    background: var(--accent-color);
}

/* WhatsApp Floating Button */
.whatsapp-float {
    position: fixed;
    bottom: 25px;
    left: 25px;
    z-index: 1000;
}

.whatsapp-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #25D366;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    font-size: 1.8rem;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.whatsapp-btn:hover {
    background: #128C7E;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    }

    50% {
        box-shadow: 0 4px 25px rgba(37, 211, 102, 0.7);
    }

    100% {
        box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    }
}

.page-banner {
    position: relative;
    height: 300px;
    color: var(--light-text);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 40px;
}

.page-banner .banner-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.page-banner .banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6));
    z-index: 0;
}

.page-banner .banner-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    padding: 0 20px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.page-banner h1 {
    font-size: 3rem;
    margin-bottom: 15px;
    text-align: center;
    width: 100%;
}

.page-banner p {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    width: 100%;
}

.catalogue-filters {
    padding: 20px 0 40px;
}

.filter-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    background-color: var(--light-bg);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-color);
}

.filter-select {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: white;
    font-family: var(--font-primary);
    cursor: pointer;
    min-width: 150px;
}

.catalogue-section {
    padding: 40px 0;
    border-bottom: 1px solid var(--border-color);
}

.catalogue-section:last-of-type {
    border-bottom: none;
}

.no-results-message {
    text-align: center;
    padding: 30px;
    margin: 20px 0;
    background-color: var(--light-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.no-results-message p {
    font-size: 1.1rem;
    color: var(--primary-color);
    margin: 0;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background-color: var(--light-bg);
}

.testimonial-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto 30px;
    overflow: hidden;
    padding: 0 10px;
    /* Add padding for the navigation buttons */
}

.testimonial-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none;
}

.testimonial-slide.active {
    opacity: 1;
    position: relative;
    pointer-events: auto;
}

.testimonial-content {
    background-color: var(--light-text);
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
    position: relative;
    z-index: 1;
    margin: 0 50px;
    /* Add margin for the navigation buttons */
}

/* Testimonial Navigation Buttons */
.testimonial-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(14, 26, 66, 0.8);
    /* Using primary color with opacity */
    color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.testimonial-nav:hover {
    background: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

.testimonial-nav:focus {
    outline: none;
}

.prev-testimonial {
    left: 0;
}

.next-testimonial {
    right: 0;
}

.testimonial-nav i {
    font-size: 1.2rem;
}

.testimonial-text {
    font-size: 1.1rem;
    font-style: italic;
    margin-bottom: 20px;
    line-height: 1.8;
    color: var(--text-color);
}

.testimonial-author {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.testimonial-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.testimonial-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ccc;
    cursor: pointer;
    transition: var(--transition);
}

.testimonial-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

/* Promo Section */
.promo {
    position: relative;
    color: var(--light-text);
    padding: 100px 0;
    text-align: center;
    overflow: hidden;
}

.promo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6));
    z-index: 1;
}

.promo img.banner-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
}

.promo-content {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
    z-index: 2;
}

.promo h2 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.promo h2:after {
    background-color: var(--accent-color);
}

.promo p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Featured Collections */
.featured-collections {
    padding: 80px 0;
}

.collection-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 250px);
    gap: 20px;
}

.collection-card {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    cursor: pointer;
}

.collection-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.collection-card:hover img {
    transform: scale(1.1);
}

.collection-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--light-text);
    transition: var(--transition);
    text-align: center;
}

.collection-overlay h3 {
    margin-bottom: 10px;
}

/* Newsletter Section */
.newsletter {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 60px 0;
    text-align: center;
}

.newsletter h2:after {
    background-color: var(--accent-color);
}

.newsletter p {
    max-width: 600px;
    margin: 0 auto 30px;
}

.newsletter-form {
    width: 500px;
    margin: 0 auto;
    display: flex;
    gap: 10px;
}

.newsletter-form input {
    width: 70%;
    flex: 1;
    padding: 15px 20px;
    border: 2px solid transparent;
    border-radius: 4px;
    font-size: 1rem;
    transition: var(--transition);
    background-color: white;
}

.newsletter-form input::placeholder {
    color: rgb(0, 0, 0);
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--accent-color);
    background-color: rgba(255, 255, 255, 0.7);
}

.newsletter-form button {
    width: 30%;
    padding: 15px 30px;
    background-color: var(--accent-color);
    color: var(--primary-color);
    border: none;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: var(--transition);
}

.newsletter-form button:hover {
    background-color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Footer */
footer {
    background-color: var(--dark-bg);
    color: var(--light-text);
    padding: 60px 0 20px;
}

.footer-content {
    width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 30px;
}

.footer-logo img {
    max-height: 60px;
    margin-bottom: 15px;
}

.footer-logo p {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.footer-logo .tagline {
    font-size: 0.9rem;
    color: #aaa;
}

.footer-links h3,
.footer-contact h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
}

.footer-links h3:after,
.footer-contact h3:after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background-color: var(--accent-color);
    margin-top: 10px;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #aaa;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--accent-color);
    padding-left: 5px;
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #aaa;
}

.footer-contact .branch-info {
    margin-top: 15px;
    margin-bottom: 5px;
    color: var(--accent-color);
}

.footer-contact i {
    color: var(--accent-color);
}

.footer-contact .social-icons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.footer-contact .social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: var(--transition);
}

.footer-contact .social-icons a:hover {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.copyright {
    color: #aaa;
    margin-bottom: 0;
}

/* Founding Story Section */
.founding-story {
    padding: 80px 0;
    background-color: #fff;
}

.story-content {
    display: flex;
    gap: 50px;
    align-items: center;
}

.story-text {
    flex: 1;
}

.story-text h2 {
    color: #000000;
    font-size: 2.5rem;
    margin-bottom: 25px;
    position: relative;
}

.story-text h2:after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background: #FFD700;
    margin-top: 15px;
}

.story-text .lead {
    font-size: 1.2rem;
    color: #000000;
    margin-bottom: 20px;
    line-height: 1.6;
}

.story-text p {
    color: #000000;
    line-height: 1.8;
    margin-bottom: 15px;
}

.story-highlights {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.highlight {
    text-align: center;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.highlight:hover {
    transform: translateY(-5px);
}

.highlight i {
    color: #FFD700;
    font-size: 2rem;
    margin-bottom: 15px;
}

.highlight h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.highlight p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.story-image {
    flex: 1;
    position: relative;
}

.story-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.experience-badge {
    position: absolute;
    bottom: -20px;
    right: -20px;
    background: #FFD700;
    color: #000;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.experience-badge .years {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
}

.experience-badge .years-text {
    font-size: 0.8rem;
    margin-top: 2px;
    opacity: 0.9;
}

.experience-badge .text {
    font-size: 0.9rem;
    text-align: center;
    max-width: 80px;
    line-height: 1.2;
}



/* Fixed Grid Layouts */
.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.collection-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 250px);
    gap: 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 30px;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 50px;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 50px;
}

.story-highlights {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.mission-vision-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
}

/* Our Brands Section */
.our-brands {
    padding: 80px 0;
    text-align: center;
    background-color: #fff;
}

.our-brands h2 {
    margin-bottom: 50px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.brands-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.brand-card {
    border: 1px solid #eee;
    padding: 30px 20px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

.brand-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.brand-logo {
    margin-bottom: 20px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.brand-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.brand-description {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #666;
    text-align: center;
}

/* Home Page Styles */
.home-page header {
    position: relative;
    background-color: var(--primary-color);
}

/* Featured Promo Section - Image Slideshow */
.featured-promo {
    position: relative;
    width: 100%;
    height: 350px;
    overflow: hidden;
    margin: 30px 0;
}

.featured-promo .slideshow-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.featured-promo .slideshow-slides {
    position: relative;
    width: 100%;
    height: 100%;
}

.featured-promo .slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.featured-promo .slide.active {
    opacity: 1;
}

.featured-promo .slide img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    display: block;
    background-color: #f8f9fa;
}

/* Responsive promo images - Desktop shows PC version, Mobile shows mobile version */
.featured-promo .slide .mobile-promo-img {
    display: none;
}

.featured-promo .slide .desktop-promo-img {
    display: block;
}

/* Mobile view - show mobile image, hide desktop image */
@media (max-width: 768px) {
    .featured-promo .slide .mobile-promo-img {
        display: block;
    }

    .featured-promo .slide .desktop-promo-img {
        display: none;
    }
}

.featured-promo .slideshow-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(255, 255, 255, 0.95);
    border: 2px solid var(--primary-color);
    width: 55px;
    height: 55px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.featured-promo .slideshow-nav:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.featured-promo .prev-slide {
    left: 25px;
}

.featured-promo .next-slide {
    right: 25px;
}

.featured-promo .slideshow-controls {
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 10;
}

.featured-promo .slide-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
}

.featured-promo .slide-dot:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
}

.featured-promo .slide-dot.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    transform: scale(1.2);
}



/* About Page Styles */
.about-page header {
    position: relative;
    background-color: var(--primary-color);
}

.page-header h1 {
    color: #000;
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.page-header {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 70px 0;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.about-intro {
    padding: 80px 0;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.about-content h2 {
    margin-bottom: 30px;
    position: relative;
}

.about-content h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 15px;
}

.company-values p {
    color: #000000;
    line-height: 1.8;
    margin-bottom: 20px;
}

.about-content p {
    margin-bottom: 20px;
    line-height: 1.8;
}

.about-image img {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.values {
    padding: 80px 0;
    background-color: var(--light-bg);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    margin-top: 40px;
}

.value-card {
    background-color: var(--light-text);
    padding: 20px 15px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: var(--transition);
}

.value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.icon {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 15px;
}

.value-card h3 {
    margin-bottom: 15px;
}

.team {
    padding: 80px 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 40px;
}

.team-member {
    text-align: center;
}

.team-member img {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
    border: 5px solid var(--light-bg);
}

.team-member h3 {
    margin-bottom: 5px;
}

.position {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 15px;
}

.milestones {
    padding: 80px 0;
    background-color: var(--light-bg);
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: 40px auto 0;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 3px;
    background-color: var(--primary-color);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 50px;
    display: flex;
    align-items: center;
}

.footer-contact p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.footer-contact .branch-info {
    margin-top: 15px;
    margin-bottom: 5px;
    color: var(--accent-color);
}

.timeline-year {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 10px 20px;
    border-radius: 30px;
    font-weight: 600;
    z-index: 1;
    min-width: 100px;
    text-align: center;
}

.timeline-content {
    background-color: var(--light-text);
    padding: 20px 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    width: calc(50% - 80px);
    margin-left: 30px;
}

.timeline-content h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

.cta {
    padding: 100px 0;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/cta-bg.jpg');
    background-size: cover;
    background-position: center;
    color: var(--light-text);
    text-align: center;
}

.cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto 30px;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}



/* Contact Page Styles */
.contact-page header {
    position: relative;
    background-color: var(--primary-color);
}

.contact-section {
    padding: 80px 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
}

.contact-info {
    background-color: var(--light-bg);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.contact-info h3 {
    margin-bottom: 20px;
    position: relative;
    font-size: 1.5rem;
}

.contact-info h3:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    margin-top: 10px;
}

.branch-details {
    margin-bottom: 30px;
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.contact-info i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 25px;
}

.contact-form {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: var(--font-primary);
    font-size: 1rem;
}

.form-group textarea {
    height: 150px;
    resize: vertical;
}

.map-section {
    padding: 0 0 80px;
}

.map-container {
    height: 450px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: 0;
}

.business-hours {
    margin-top: 30px;
}

.business-hours ul {
    list-style: none;
}

.business-hours li {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px dashed var(--border-color);
}

/* Who We Are Section */
.who-we-are {
    padding: 80px 0;
    background-color: #f9f9f9;
}

.who-we-content h2 {
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 30px;
    position: relative;
}

.who-we-content h2:after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background: #FFD700;
    margin-top: 15px;
}

.company-description .lead {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #444;
    margin-bottom: 30px;
}

.sub-heading {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 25px;
    font-weight: 500;
}

.subsidiary-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin: 40px 0;
}

.subsidiary-card {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
    text-align: center;
}

.subsidiary-card:hover {
    transform: translateY(-5px);
}

.subsidiary-card i {
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 20px;
}

.subsidiary-card h3 {
    color: #333;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.subsidiary-card p {
    color: #666;
    line-height: 1.6;
}

.company-values p {
    color: #000000;
    line-height: 1.8;
    margin-bottom: 20px;
}

/* Mission and Vision Section */
.mission-vision {
    padding: 80px 0;
    background: linear-gradient(135deg, #fff 50%, #f9f9f9 50%);
}

.mission-vision-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    margin-top: 20px;
}

.vision-box,
.mission-box {
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    background: #fff;
    transition: transform 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.vision-box:hover,
.mission-box:hover {
    transform: translateY(-5px);
}

.mission-box {
    background: #FFD700;
}

.vision-box .content,
.mission-box .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.mission-vision .icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    font-size: 2rem;
}

.vision-box .icon {
    background: #FFD700;
    color: #000;
}

.mission-box .icon {
    background: #fff;
    color: #FFD700;
}

.mission-vision h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
}

.mission-vision h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
}

.vision-box h2:after {
    background: #FFD700;
}

.mission-box h2:after {
    background: #fff;
}

.vision-box h2 {
    color: #333;
}

.mission-box h2,
.mission-box p {
    color: #000;
}

.vision-box p {
    color: #555;
    line-height: 1.8;
    font-size: 1.1rem;
}

.mission-box p {
    line-height: 1.8;
    font-size: 1.1rem;
}

/* Core Values Section */
.core-values {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a2a5e 100%);
    color: var(--light-text);
}

.core-values h2 {
    color: var(--light-text);
    margin-bottom: 50px;
}

.core-values h2:after {
    background: var(--accent-color);
}

.core-values .values-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 30px;
    margin-top: 20px;
}

.core-values .value-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px 25px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.core-values .value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.core-values .value-card .icon {
    width: 70px;
    height: 70px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.core-values .value-card .icon i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.core-values .value-card h3 {
    color: var(--accent-color);
    font-size: 1.4rem;
    margin-bottom: 15px;
}

.core-values .value-card p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
}

/* Page Header */
.page-header {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 60px 0;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.breadcrumb {
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--light-text);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Desktop Section Styles */
.founding-story,
.who-we-are,
.mission-vision {
    padding: 80px 0;
}

.story-content {
    display: flex;
    gap: 50px;
    align-items: center;
}

.subsidiary-cards {
    grid-template-columns: repeat(3, 1fr);
}

.mission-vision-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
}



/* Fixed width containers for sections */
.section-content {
    width: 1200px;
    margin: 0 auto;
}

.about-content,
contact-content,
.products-content {
    width: 1000px;
    margin: 0 auto;
}

/* Newsletter fixed layout */
.newsletter-form {
    width: 500px;
    margin: 0 auto;
    display: flex;
}

.newsletter-form input {
    width: 70%;
}

.newsletter-form button {
    width: 30%;
}

/* Footer fixed layout */
.footer-content {
    width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 30px;
}

/* News Page Styles */
.news-page header {
    position: relative;
    background-color: var(--primary-color);
}

.news-section {
    padding: 60px 0;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.news-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.news-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-date {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color);
    color: var(--light-text);
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    min-width: 60px;
}

.news-date .day {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1;
}

.news-date .month {
    display: block;
    font-size: 0.8rem;
    text-transform: uppercase;
    margin: 2px 0;
}

.news-date .year {
    display: block;
    font-size: 0.7rem;
    opacity: 0.8;
}

.news-content {
    padding: 25px;
}

.news-content h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    line-height: 1.3;
}

.news-content p {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 20px;
}

.read-more {
    color: var(--secondary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    transition: var(--transition);
}

.read-more:hover {
    color: var(--primary-color);
}

/* Product Details Modal */
.product-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    overflow-y: auto;
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 900px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-bg);
    border-radius: 12px 12px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.8rem;
}

.modal-close {
    font-size: 2rem;
    font-weight: bold;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-close:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
}

.modal-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    padding: 30px;
}

.modal-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.modal-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.product-category {
    background-color: var(--secondary-color);
    color: var(--light-text);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    align-self: flex-start;
}

.product-price {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 10px 0;
}

.product-description {
    color: var(--text-color);
    line-height: 1.6;
    font-size: 1.1rem;
}

.product-features h3,
.product-specifications h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 5px;
}

.product-features ul {
    list-style: none;
    padding: 0;
}

.product-features li {
    padding: 8px 0;
    position: relative;
    padding-left: 25px;
    color: var(--text-color);
}

.product-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
    font-size: 1.1rem;
}

.spec-item {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-item strong {
    color: var(--primary-color);
    min-width: 120px;
    display: inline-block;
}

.modal-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    padding: 20px 30px;
    border-top: 1px solid var(--border-color);
    background-color: var(--light-bg);
    border-radius: 0 0 12px 12px;
}

.modal-actions .btn-primary,
.modal-actions .btn-secondary {
    flex: 1;
    text-align: center;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
}

/* Article Page Styles */
.article-section {
    padding: 40px 0;
    background-color: white;
}

.article-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.article-header {
    margin-bottom: 30px;
}

.article-author {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 10px;
    font-weight: 500;
}

.article-header h1 {
    color: var(--primary-color);
    font-size: 2.2rem;
    line-height: 1.3;
    margin: 0;
    font-weight: 700;
}

.article-image {
    margin-bottom: 30px;
}

.article-image img {
    width: 100%;
    height: auto;
    border-radius: 4px;
}

.article-text h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 2rem;
}

.article-text h3 {
    color: var(--primary-color);
    margin: 30px 0 15px;
    font-size: 1.3rem;
}

.article-text p {
    line-height: 1.8;
    margin-bottom: 20px;
    color: var(--text-color);
}

.article-text ul {
    margin: 20px 0;
    padding-left: 20px;
}

.article-text li {
    margin-bottom: 8px;
    color: var(--text-color);
}

.article-text blockquote {
    background-color: var(--light-bg);
    border-left: 4px solid var(--secondary-color);
    padding: 20px;
    margin: 30px 0;
    font-style: italic;
}

.article-text blockquote cite {
    display: block;
    margin-top: 10px;
    font-weight: 600;
    color: var(--primary-color);
}

.article-tags {
    margin: 30px 0;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.tag {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-right: 10px;
    margin-bottom: 10px;
}

.article-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
}

.share-buttons {
    display: flex;
    align-items: center;
    gap: 15px;
}

.share-buttons span {
    color: var(--text-color);
    font-weight: 600;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: white;
    transition: var(--transition);
}

.share-btn.facebook {
    background-color: #3b5998;
}

.share-btn.twitter {
    background-color: #1da1f2;
}

.share-btn.linkedin {
    background-color: #0077b5;
}

.share-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.related-news {
    padding: 80px 0;
}

.related-news h2 {
    text-align: center;
    margin-bottom: 50px;
    color: var(--primary-color);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--light-text);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--accent-color);
    transform: translateY(-5px);
}

/* Awards Gallery Styles */
.awards-gallery {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
    margin-top: 2rem;
    padding: 2rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.award-item {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    transition: transform 0.3s ease;
    aspect-ratio: 4/3;
}

.award-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.award-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.award-caption {
    padding: 1rem;
    text-align: center;
    font-size: 0.9rem;
    color: #333;
    background: #f8f8f8;
    margin: 0;
}

@media (max-width: 768px) {
    .awards-gallery {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 1rem;
    }

    .award-item {
        aspect-ratio: 3/2;
    }
}

/* Image Lightbox Styles */
.lightbox-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.lightbox-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: var(--light-text);
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    transition: var(--transition);
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.lightbox-close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* Make product images clickable */
.product-image img {
    cursor: pointer;
    transition: var(--transition);
}

.product-image img:hover {
    transform: scale(1.05);
    opacity: 0.9;
}