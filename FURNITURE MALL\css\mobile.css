/* Mobile Styles for Furniture Mall */

/* Mobile Menu Styles */
@media (max-width: 768px) {
    nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100vh;
        background-color: var(--primary-color);
        padding: 80px 20px 20px;
        transition: left 0.3s ease;
        z-index: 999;
        overflow-y: auto;
    }

    nav .nav-links {
        display: flex !important;
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }
}

.mobile-menu {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--light-text);
    z-index: 1001;
}

.mobile-menu.active i {
    transform: rotate(90deg);
}

.mobile-menu i {
    transition: transform 0.3s ease;
}

/* Testimonial Navigation Buttons */
.testimonial-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(14, 26, 66, 0.8);
    /* Using primary color with opacity */
    color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;
}

.testimonial-nav:hover {
    background: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

.prev-testimonial {
    left: 0;
}

.next-testimonial {
    right: 0;
}

.testimonial-nav i {
    font-size: 1.2rem;
}

/* Adjusting testimonial content for navigation buttons */
.testimonial-content {
    margin: 0 50px;
    padding: 30px;
}

/* Mobile Specific Styles */
@media (max-width: 768px) {

    /* Header and Navigation */
    .mobile-menu {
        display: block;
    }

    .nav-links a {
        display: block;
        padding: 10px 0;
        font-size: 1.1rem;
        color: var(--light-text);
    }

    /* Override the display: none from style.css */
    header nav .nav-links {
        display: flex !important;
    }

    /* Testimonials */
    .testimonial-nav {
        width: 35px;
        height: 35px;
    }

    .testimonial-nav i {
        font-size: 1rem;
    }

    .testimonial-content {
        margin: 0 40px;
        padding: 20px;
    }

    /* Collection Grid */
    .collection-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }

    .collection-card {
        height: 220px;
    }

    /* Showroom Section - Hide images in mobile view */
    .showroom-images {
        display: none;
    }

    .showroom-grid {
        grid-template-columns: 1fr !important;
        gap: 30px;
    }

    .showroom-content {
        text-align: center;
    }

    .showroom-content h2:after {
        margin: 15px auto 0;
    }

    .showroom-content .btn-primary {
        display: block;
        margin: 20px auto 0;
        max-width: 150px;
        text-align: center;
    }

    /* Product Grid */
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    /* Catalogue Page Mobile Styles */
    .page-banner {
        height: 200px;
    }

    .page-banner h1 {
        font-size: 2.2rem;
    }

    .page-banner p {
        font-size: 0.9rem;
    }

    .filter-container {
        flex-direction: column;
        align-items: stretch;
        padding: 15px;
    }

    .filter-group {
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-select {
        width: 100%;
    }

    .catalogue-section {
        padding: 30px 0;
    }

    .catalogue-section h2 {
        font-size: 1.8rem;
    }

    /* Privacy Policy Page Mobile */
    .privacy-content {
        padding: 60px 0;
    }

    .privacy-policy {
        padding: 30px 20px;
        margin: 0 15px;
    }

    .policy-header h2 {
        font-size: 1.8rem;
    }

    .policy-header h3 {
        font-size: 1.3rem;
    }

    .policy-section h3 {
        font-size: 1.2rem;
    }

    .contact-details {
        padding: 15px;
    }

    /* Contact Page - Show form first in mobile view */
    .contact-grid {
        display: grid;
        grid-template-columns: 1fr;
    }

    .contact-grid .contact-form {
        order: -1;
        /* This makes the form appear first */
        margin-bottom: 30px;
        padding: 25px 20px;
    }

    .contact-grid .contact-info {
        padding: 25px 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        margin-bottom: 5px;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 10px;
    }

    .form-group textarea {
        height: 120px;
    }
}

@media (max-width: 576px) {

    /* Header */
    .logo {
        max-height: 50px;
    }

    /* Hero Section */
    .hero {
        height: 100vh;
    }

    .hero h1 {
        font-size: 2.2rem;
    }

    .hero p {
        font-size: 0.9rem;
        max-width: 100%;
    }

    /* Testimonials */
    .testimonial-nav {
        width: 30px;
        height: 30px;
    }

    .testimonial-content {
        margin: 0 35px;
        padding: 15px;
    }

    .testimonial-text {
        font-size: 0.9rem;
    }

    /* Product Grid */
    .product-grid {
        grid-template-columns: 1fr;
    }

    /* Showroom Section - Adjust button for smaller screens */
    .showroom-content .btn-primary {
        max-width: 130px;
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    /* Privacy Policy Page Small Mobile */
    .privacy-content {
        padding: 40px 0;
    }

    .privacy-policy {
        padding: 25px 15px;
        margin: 0 10px;
    }

    .policy-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
    }

    .policy-header h2 {
        font-size: 1.6rem;
    }

    .policy-header h3 {
        font-size: 1.2rem;
    }

    .policy-section {
        margin-bottom: 25px;
    }

    .policy-section h3 {
        font-size: 1.1rem;
    }

    .policy-footer {
        margin-top: 30px;
        padding-top: 20px;
    }

    /* Contact Page - Ensure form is first on very small screens */
    .contact-grid .contact-form {
        margin-bottom: 25px;
        padding: 20px 15px;
    }

    .contact-grid .contact-info {
        padding: 20px 15px;
    }

    .contact-section {
        padding: 60px 0;
    }

    .map-section {
        padding: 0 0 60px;
    }

    .map-container {
        height: 350px;
    }

    /* Back to Top Button - Mobile */
    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    /* Product Modal Mobile Styles */
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-header h2 {
        font-size: 1.5rem;
    }

    .modal-body {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }

    .modal-image img {
        height: 250px;
    }

    .product-price {
        font-size: 1.5rem;
    }

    .modal-actions {
        flex-direction: column;
        gap: 10px;
    }

    .modal-actions .btn-primary,
    .modal-actions .btn-secondary {
        width: 100%;
    }

    /* Catalogue Sidebar Mobile Styles */
    .catalogue-layout {
        flex-direction: column;
        padding: 0 15px;
    }

    .catalogue-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100vh;
        z-index: 1001;
        transition: left 0.3s ease;
        overflow-y: auto;
        padding-top: 80px;
    }

    .catalogue-sidebar.active {
        left: 0;
    }

    .mobile-sidebar-toggle {
        display: block !important;
        margin-bottom: 20px;
    }

    .catalogue-main {
        width: 100%;
    }

    /* WhatsApp Button Mobile */
    .whatsapp-btn {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        bottom: 20px;
        left: 20px;
    }

    /* Sidebar Overlay */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
    }

    .sidebar-overlay.active {
        display: block;
    }
}